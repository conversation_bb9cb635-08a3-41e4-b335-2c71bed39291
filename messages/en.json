{"common": {"name": "Name", "email": "Email", "mobile": "Mobile Number", "work": "Work", "submit": "Submit", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "print": "Print", "download": "Download", "back": "Back", "next": "Next", "previous": "Previous", "loading": "Loading...", "success": "Success", "error": "Error", "required": "This field is required", "invalidEmail": "Please enter a valid email address"}, "navigation": {"home": "Reservation System", "guest": "Guest Registration", "company": "Company Registration", "admin": "Admin", "dashboard": "Dashboard", "logout": "Logout"}, "guest": {"title": "Guest Registration", "subtitle": "Please fill in your information to register", "form": {"name": "Full Name", "email": "Email Address", "mobile": "Mobile Number", "work": "Work/Profession"}, "success": "Registration successful! Check your email for your QR code.", "emailExists": "This email is already registered."}, "company": {"title": "Company Registration", "subtitle": "Please fill in your company information", "form": {"responsible": "Responsible Person", "workName": "Work/Company Name", "position": "Position", "whatsapp": "WhatsApp Number", "workField": "Work Field", "email": "Email Address", "howKnew": "How did you know about the exhibition?", "howKnewOptions": {"facebook": "Facebook", "email": "Email", "twitter": "X Platform", "whatsapp": "WhatsApp Messages", "instagram": "Instagram", "search": "Search Engines", "radio": "Radio/TV", "other": "Someone else"}}, "success": "Company registration successful! Check your email for your QR code."}, "admin": {"title": "Admin Dashboard", "login": {"title": "<PERSON><PERSON>", "subtitle": "Sign in to access the admin dashboard", "email": "Email", "password": "Password", "submit": "<PERSON><PERSON>", "error": "Invalid credentials", "demoCredentials": "Demo credentials:"}, "dashboard": {"stats": {"totalGuests": "Total Guests", "totalCompanies": "Total Companies", "scannedCodes": "Scanned QR Codes", "joinedGuests": "Joined Guests", "joinedCompanies": "Joined Companies"}, "actions": {"scanQR": "Scan QR Code", "viewGuests": "View Guests", "viewCompanies": "View Companies"}, "descriptions": {"viewGuests": "View and manage all registered guests", "viewCompanies": "View and manage all registered companies", "scanQR": "Scan QR codes for guests and companies"}}, "scanner": {"title": "QR Code Scanner", "scanning": "Scanning...", "success": "QR Code scanned successfully!", "invalid": "Invalid QR Code", "alreadyScanned": "This QR Code has already been scanned"}, "tables": {"guests": "Guests", "companies": "Companies", "name": "Name", "email": "Email", "mobile": "Mobile", "work": "Work", "status": "Status", "joinedAt": "Joined At", "actions": "Actions"}}, "card": {"title": "Your Registration Card", "subtitle": "Show this QR code at the exhibition", "download": "Download Card", "downloadQR": "Download QR", "print": "Print Card", "share": "Share", "guestCard": "Exhibition Guest Card", "companyCard": "Exhibition Company Card", "showAtEntry": "Show this code at entry", "registrationDate": "Registration Date", "exhibitionName": "Libya International Exhibition", "exhibitionYear": "2024"}, "home": {"title": "Welcome to Exhibition Reservation System", "subtitle": "Register as a guest or company to participate in the exhibition and get your QR code", "guestCard": {"title": "Guest Registration", "description": "Register as an individual visitor to get your exhibition access card", "button": "Register as Guest"}, "companyCard": {"title": "Company Registration", "description": "Register your company to participate in the exhibition", "button": "Register Company"}, "adminCard": {"title": "Admin Access", "description": "Access the admin dashboard to manage registrations", "button": "<PERSON><PERSON>"}}, "messages": {"redirecting": "Redirecting to your card...", "redirectingCompany": "Redirecting to your company card...", "registrationFailed": "Registration failed", "errorOccurred": "An error occurred", "allFieldsRequired": "All fields are required", "invalidCredentials": "Invalid credentials", "internalServerError": "Internal server error", "guestNotFound": "Guest not found", "companyNotFound": "Company not found", "linkCopied": "Link copied to clipboard", "welcomeToDashboard": "Welcome to your admin dashboard", "scanDescription": "Scan guest and company QR codes to register their entry", "alreadyScanned": "This code was already scanned", "scanAnother": "<PERSON><PERSON>", "cameraScanner": "Camera Scanner", "useCameraToScan": "Use device camera to scan QR codes", "stop": "Stop", "uploadImage": "Upload Image", "uploadImageDescription": "Upload an image containing a QR code", "chooseFile": "Choose Image", "totalGuests": "Total Guests", "totalCompanies": "Total Companies", "scanned": "Scanned", "exportCSV": "Export CSV", "filterData": "Filter Data", "search": "Search", "searchPlaceholder": "Search by name, email, phone...", "pageNotFound": "Page Not Found", "pageNotFoundDescription": "The page you are looking for does not exist.", "goToEnglish": "Go to English", "goToArabic": "الذهاب للعربية", "status": "Status", "all": "All", "scannedStatus": "Scanned", "notScanned": "Not Scanned", "noResultsFound": "No results found", "failedToProcessQR": "Failed to process QR code", "failedToFetchGuests": "Failed to fetch guests", "failedToFetchCompanies": "Failed to fetch companies", "registrationDate": "Registration Date", "scannedDate": "Scanned Date", "workField": "Work Field", "howKnew": "How Knew", "position": "Position", "whatsapp": "WhatsApp", "failedToAccessCamera": "Failed to access camera. Please check permissions.", "couldNotReadQR": "Could not read QR code from image", "loginFailed": "<PERSON><PERSON> failed", "startScanning": "Start Scanning", "searchCompaniesPlaceholder": "Search by company, responsible, email...", "cameraPermissionDenied": "Camera access denied", "cameraPermissionDeniedHelp": "Camera permission is required to scan QR codes. Please enable camera access in your browser settings and try again.", "noCameraFound": "No camera found on this device", "checkingPermissions": "Checking camera permissions...", "allowCameraAccess": "Allow Camera Access", "recheckPermissions": "Check Permissions Again"}}