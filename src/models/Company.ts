import mongoose, { Document, Schema } from "mongoose";

export interface ICompany extends Document {
  responsible: string;
  workName: string;
  position: string;
  whatsapp: string;
  workField: string;
  email: string;
  howKnew: string;
  qrCode: string;
  isScanned: boolean;
  scannedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const CompanySchema = new Schema<ICompany>(
  {
    responsible: {
      type: String,
      required: [true, "Responsible person is required"],
      trim: true,
    },
    workName: {
      type: String,
      required: [true, "Work/Company name is required"],
      trim: true,
    },
    position: {
      type: String,
      required: [true, "Position is required"],
      trim: true,
    },
    whatsapp: {
      type: String,
      required: [true, "WhatsApp number is required"],
      trim: true,
    },
    workField: {
      type: String,
      required: [true, "Work field is required"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      lowercase: true,
      trim: true,
    },
    howKnew: {
      type: String,
      required: [true, "How you knew about the exhibition is required"],
      enum: [
        "facebook",
        "email",
        "twitter",
        "whatsapp",
        "instagram",
        "search",
        "radio",
        "other",
      ],
    },
    qrCode: {
      type: String,
      required: true,
      unique: true,
    },
    isScanned: {
      type: Boolean,
      default: false,
    },
    scannedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Create index for email uniqueness
CompanySchema.index({ email: 1 }, { unique: true });

export default mongoose.models.Company ||
  mongoose.model<ICompany>("Company", CompanySchema);
