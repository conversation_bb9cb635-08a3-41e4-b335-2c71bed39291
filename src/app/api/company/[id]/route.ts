import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/mongodb";
import Company from "@/models/Company";
import { generateQRCode } from "@/lib/qrcode";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;
    const company = await Company.findById(id);

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Generate QR code data URL for display
    const companyData = {
      id: company.qrCode,
      responsible: company.responsible,
      workName: company.workName,
      position: company.position,
      whatsapp: company.whatsapp,
      workField: company.workField,
      email: company.email,
      howKnew: company.howKnew,
      type: "company",
    };

    const qrCodeDataURL = await generateQRCode(companyData, "company");

    return NextResponse.json({
      company: {
        _id: company._id.toString(),
        responsible: company.responsible,
        workName: company.workName,
        position: company.position,
        whatsapp: company.whatsapp,
        workField: company.workField,
        email: company.email,
        howKnew: company.howKnew,
        qrCode: company.qrCode,
        createdAt: company.createdAt,
      },
      qrCodeDataURL,
    });
  } catch (error) {
    console.error("Error fetching company:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
