"use client";

import { useState, useEffect, useRef } from "react";
import { useTranslations, useLocale } from "next-intl";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Navigation from "@/components/Navigation";
import { Download, Printer } from "lucide-react";

interface CompanyData {
  _id: string;
  responsible: string;
  workName: string;
  position: string;
  whatsapp: string;
  workField: string;
  email: string;
  howKnew: string;
  qrCode: string;
  createdAt: string;
}

export default function CompanyCard({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) {
  const [id, setId] = useState<string>("");
  const t = useTranslations();
  const locale = useLocale();

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setId(resolvedParams.id);
    };
    getParams();
  }, [params]);
  const cardRef = useRef<HTMLDivElement>(null);

  const [company, setCompany] = useState<CompanyData | null>(null);
  const [qrCodeDataURL, setQrCodeDataURL] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (id) {
      fetchCompanyData();
    }
  }, [id]);

  const fetchCompanyData = async () => {
    try {
      const response = await fetch(`/api/company/${id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch company data");
      }

      setCompany(data.company);
      setQrCodeDataURL(data.qrCodeDataURL);
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const downloadQROnly = () => {
    if (!qrCodeDataURL || !company) return;

    const link = document.createElement("a");
    link.download = `company-qr-${company.workName.replace(/\s+/g, "-")}.png`;
    link.href = qrCodeDataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const printCard = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100">
        <Navigation />
        <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t("common.loading")}</p>
          </div>
        </main>
      </div>
    );
  }

  if (error || !company) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100">
        <Navigation />
        <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <Alert variant="destructive">
            <AlertDescription>
              {error || t("messages.companyNotFound")}
            </AlertDescription>
          </Alert>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100">
      <Navigation />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="space-y-6">
          {/* Card Preview */}
          <div
            ref={cardRef}
            className="print-area bg-white p-8 rounded-lg shadow-lg"
          >
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {t("card.companyCard")}
              </h1>
              <div className="w-20 h-1 bg-green-600 mx-auto"></div>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="md:col-span-2 space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      {t("company.form.workName")}
                    </label>
                    <p className="text-lg font-semibold text-gray-900">
                      {company.workName}
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      {t("company.form.responsible")}
                    </label>
                    <p className="text-lg font-semibold text-gray-900">
                      {company.responsible}
                    </p>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      {t("company.form.position")}
                    </label>
                    <p className="text-gray-900">{company.position}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      {t("company.form.whatsapp")}
                    </label>
                    <p className="text-gray-900">{company.whatsapp}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">
                    {t("company.form.workField")}
                  </label>
                  <p className="text-gray-900">{company.workField}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">
                    {t("common.email")}
                  </label>
                  <p className="text-gray-900">{company.email}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">
                    {t("company.form.howKnew")}
                  </label>
                  <p className="text-gray-900">
                    {t(`company.form.howKnewOptions.${company.howKnew}`)}
                  </p>
                </div>
              </div>

              <div className="text-center">
                {qrCodeDataURL && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <img
                      src={qrCodeDataURL}
                      alt="QR Code"
                      className="mx-auto mb-2"
                      style={{ width: "200px", height: "200px" }}
                    />
                    <p className="text-sm text-gray-600">
                      {t("card.showAtEntry")}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 text-center text-sm text-gray-500">
              {t("card.registrationDate")}:{" "}
              {new Date(company.createdAt).toLocaleDateString(
                locale === "ar" ? "ar-SA" : "en-US"
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <Card className="no-print">
            <CardHeader>
              <CardTitle>{t("card.title")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Button
                  onClick={downloadQROnly}
                  variant="outline"
                  className="flex-1"
                >
                  <Download className="h-4 w-4 mr-2" />
                  {t("card.downloadQR")}
                </Button>
                <Button
                  onClick={printCard}
                  variant="outline"
                  className="flex-1"
                >
                  <Printer className="h-4 w-4 mr-2" />
                  {t("card.print")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
