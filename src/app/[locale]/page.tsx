import { getTranslations } from "next-intl/server";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Navigation from "@/components/Navigation";
import { Users, Building2 } from "lucide-react";

export default async function Home({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Navigation />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t("home.title")}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("home.subtitle")}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <CardTitle className="text-2xl">
                {t("home.guestCard.title")}
              </CardTitle>
              <CardDescription>
                {t("home.guestCard.description")}
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Link href={`/${locale}/guest`}>
                <Button size="lg" className="w-full" variant="secondary">
                  {t("home.guestCard.button")}
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Building2 className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-2xl">
                {t("home.companyCard.title")}
              </CardTitle>
              <CardDescription>
                {t("home.companyCard.description")}
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Link href={`/${locale}/company`}>
                <Button size="lg" className="w-full" variant="secondary">
                  {t("home.companyCard.button")}
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
